# Windows-compatible requirements for BGE-M3 Embedding Server
# Use this if the main requirements.txt fails on Windows

# Core FastAPI packages
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic>=2.0.0
python-multipart==0.0.6
aiofiles==23.2.1
python-dotenv==1.0.0
loguru==0.7.2

# Basic dependencies
numpy>=1.21.0,<2.0.0
requests>=2.28.0

# PyTorch (CPU version for compatibility)
--index-url https://download.pytorch.org/whl/cpu
torch==2.1.2+cpu
torchvision==0.16.2+cpu
torchaudio==2.1.2+cpu

# Hugging Face ecosystem
transformers>=4.35.0
tokenizers>=0.15.0
huggingface-hub>=0.19.0

# Try sentence-transformers (may need manual installation)
sentence-transformers>=2.2.0

# Optional text processing
nltk>=3.8.1
regex>=2023.10.3
