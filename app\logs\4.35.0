Collecting transformers
  Using cached transformers-4.53.0-py3-none-any.whl.metadata (39 kB)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (3.18.0)
Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (0.31.4)
Requirement already satisfied: numpy>=1.17 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (2.2.6)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (24.2)
Requirement already satisfied: pyyaml>=5.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (6.0.2)
Requirement already satisfied: regex!=2019.12.17 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (2024.11.6)
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (2.32.3)
Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (0.21.1)
Collecting safetensors>=0.4.3 (from transformers)
  Downloading safetensors-0.5.3-cp38-abi3-win_amd64.whl.metadata (3.9 kB)
Requirement already satisfied: tqdm>=4.27 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from transformers) (4.67.1)
Requirement already satisfied: fsspec>=2023.5.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (2025.3.2)
Requirement already satisfied: typing-extensions>=3.7.4.3 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.13.2)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from tqdm>=4.27->transformers) (0.4.6)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from requests->transformers) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from requests->transformers) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from requests->transformers) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from requests->transformers) (2025.4.26)
Downloading transformers-4.53.0-py3-none-any.whl (10.8 MB)
   ---------------------------------------- 10.8/10.8 MB 36.4 MB/s eta 0:00:00
Downloading safetensors-0.5.3-cp38-abi3-win_amd64.whl (308 kB)
Installing collected packages: safetensors, transformers

Successfully installed safetensors-0.5.3 transformers-4.53.0
