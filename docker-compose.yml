version: '3.8'

services:
  embedding-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bge-m3-embedding-server
    ports:
      - "8000:8000"
    environment:
      - MODEL_NAME=BAAI/bge-m3
      - DEVICE=auto
      - LOG_LEVEL=INFO
      - MAX_CONCURRENT_REQUESTS=10
      - BATCH_SIZE=32
    volumes:
      - ./models:/app/models  # Cache models locally
      - ./logs:/app/logs      # Persist logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
