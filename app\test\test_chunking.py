#!/usr/bin/env python3
"""
Test script for optimized chunking functionality
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_optimized_chunking():
    """Test the optimized chunking with different texts"""
    
    # Test text 1: Technical content
    tech_text = """
    Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
    có thể thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. <PERSON><PERSON><PERSON><PERSON> này bao gồm học tập, 
    l<PERSON> lu<PERSON>, gi<PERSON><PERSON> quyết vấn đề, nhận thức và hiểu ngôn ngữ tự nhiên.
    
    Machine learning là một nhánh con của AI tập trung vào việc phát triển các thuật toán và mô hình 
    thống kê cho phép máy tính học và cải thiện hiệu suất từ dữ liệu mà không cần được lập trình 
    một cách rõ ràng cho từng nhiệm vụ cụ thể.
    
    Deep learning là một phương pháp machine learning sử dụng mạng neural nhân tạo với nhiều lớp 
    để mô hình hóa và hiểu dữ liệu phức tạp. Nó đã đạt được những thành công đáng kể trong nhiều 
    lĩnh vực như xử lý hình ảnh, nhận dạng giọng nói và xử lý ngôn ngữ tự nhiên.
    
    Natural Language Processing (NLP) là một lĩnh vực con của trí tuệ nhân tạo tập trung vào 
    việc cho phép máy tính hiểu, diễn giải và tạo ra ngôn ngữ tự nhiên của con người. 
    NLP kết hợp khoa học máy tính, trí tuệ nhân tạo và ngôn ngữ học để xử lý và phân tích 
    lượng lớn dữ liệu ngôn ngữ tự nhiên.
    
    Computer Vision là một lĩnh vực khác của AI tập trung vào việc cho phép máy tính "nhìn" và 
    hiểu thông tin từ hình ảnh và video. Nó sử dụng các thuật toán để xử lý, phân tích và hiểu 
    nội dung hình ảnh, từ đó có thể nhận dạng đối tượng, phát hiện khuôn mặt, và thực hiện 
    nhiều tác vụ thị giác khác.
    """
    
    # Test different chunk sizes
    test_configs = [
        {"chunk_size": 200, "overlap": 30, "method": "semantic"},
        {"chunk_size": 300, "overlap": 50, "method": "semantic"},
        {"chunk_size": 384, "overlap": 64, "method": "semantic"},  # Default optimized
        {"chunk_size": 500, "overlap": 80, "method": "semantic"},
    ]
    
    print("🔍 Testing Optimized Chunking")
    print("=" * 60)
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n📊 Test {i}: chunk_size={config['chunk_size']}, overlap={config['overlap']}")
        print("-" * 50)
        
        payload = {
            "text": tech_text,
            **config
        }
        
        try:
            response = requests.post(f"{BASE_URL}/chunk", json=payload)
            if response.status_code == 200:
                result = response.json()
                chunks = result['chunks']
                metadata = result['metadata']
                
                print(f"✅ Status: Success")
                print(f"📈 Total chunks: {len(chunks)}")
                print(f"📏 Original length: {metadata['original_length']} chars")
                
                # Analyze chunk balance
                chunk_lengths = [len(chunk) for chunk in chunks]
                avg_length = sum(chunk_lengths) / len(chunk_lengths)
                min_length = min(chunk_lengths)
                max_length = max(chunk_lengths)
                
                print(f"📊 Chunk lengths - Avg: {avg_length:.1f}, Min: {min_length}, Max: {max_length}")
                print(f"⚖️  Balance ratio: {min_length/max_length:.2f} (closer to 1.0 = better balance)")
                
                # Show first few chunks with lengths
                print(f"\n📝 Chunks preview:")
                for j, chunk in enumerate(chunks[:3]):
                    print(f"  Chunk {j+1} ({len(chunk)} chars): {chunk[:80]}...")
                
                if len(chunks) > 3:
                    print(f"  ... and {len(chunks)-3} more chunks")
                    
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    # Test with different text types
    print(f"\n\n🔍 Testing Different Text Types")
    print("=" * 60)
    
    test_texts = [
        {
            "name": "Short paragraphs",
            "text": "Đây là đoạn ngắn một. Nó chỉ có vài câu. Đây là đoạn ngắn hai. Cũng chỉ có vài câu thôi. Đây là đoạn ngắn ba. Tương tự như trên."
        },
        {
            "name": "Long sentences", 
            "text": "Đây là một câu rất dài với nhiều thông tin chi tiết về trí tuệ nhân tạo, machine learning, deep learning và các ứng dụng của chúng trong thực tế như xử lý ngôn ngữ tự nhiên, computer vision, và robotics. Đây là câu thứ hai cũng rất dài với nhiều thông tin về các thuật toán, mô hình toán học, và phương pháp tối ưu hóa được sử dụng trong các hệ thống AI hiện đại."
        }
    ]
    
    for test_case in test_texts:
        print(f"\n📝 Testing: {test_case['name']}")
        print("-" * 30)
        
        payload = {
            "text": test_case['text'],
            "chunk_size": 384,
            "overlap": 64,
            "method": "semantic"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/chunk", json=payload)
            if response.status_code == 200:
                result = response.json()
                chunks = result['chunks']
                
                chunk_lengths = [len(chunk) for chunk in chunks]
                print(f"Chunks: {len(chunks)}, Lengths: {chunk_lengths}")
                
                for j, chunk in enumerate(chunks):
                    print(f"  {j+1}. ({len(chunk)}): {chunk}")
            else:
                print(f"❌ Error: {response.status_code}")
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_optimized_chunking()
