from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Union
import asyncio
import logging
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

from .models.embedding_model import EmbeddingModel
from .services.text_chunker import <PERSON><PERSON>hunker
from .config import settings

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for models
embedding_model = None
text_chunker = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle - startup and shutdown events"""
    global embedding_model, text_chunker
    
    # Startup
    logger.info("Starting Embedding Server...")
    try:
        # Initialize BGE-M3 model
        embedding_model = EmbeddingModel(model_name=settings.MODEL_NAME)
        await embedding_model.load_model()
        
        # Initialize text chunker
        text_chunker = TextChunker()
        await text_chunker.initialize()
        
        logger.info("Models loaded successfully!")
    except Exception as e:
        logger.error(f"Failed to load models: {e}")
        raise e
    
    yield
    
    # Shutdown
    logger.info("Shutting down Embedding Server...")


# Create FastAPI app
app = FastAPI(
    title="BGE-M3 Embedding Server",
    description="A FastAPI server for text embedding and chunking using BGE-M3 model",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models for request/response
class TextInput(BaseModel):
    text: str = Field(..., description="Input text to process")


class TextListInput(BaseModel):
    texts: List[str] = Field(..., description="List of texts to process")


class ChunkingRequest(BaseModel):
    text: str = Field(..., description="Text to chunk")
    chunk_size: Optional[int] = Field(512, description="Maximum chunk size in characters")
    overlap: Optional[int] = Field(50, description="Overlap between chunks")
    method: Optional[str] = Field("semantic", description="Chunking method: 'semantic' or 'fixed'")


class EmbeddingRequest(BaseModel):
    text: Union[str, List[str]] = Field(..., description="Text or list of texts to embed")
    normalize: Optional[bool] = Field(True, description="Whether to normalize embeddings")


class ChunkingResponse(BaseModel):
    chunks: List[str] = Field(..., description="List of text chunks")
    metadata: dict = Field(..., description="Metadata about chunking process")


class EmbeddingResponse(BaseModel):
    embeddings: List[List[float]] = Field(..., description="List of embedding vectors")
    metadata: dict = Field(..., description="Metadata about embedding process")


@app.get("/")
async def root():
    """Root endpoint with basic information"""
    return {
        "message": "BGE-M3 Embedding Server",
        "version": "1.0.0",
        "model": settings.MODEL_NAME,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    global embedding_model, text_chunker
    
    if embedding_model is None or text_chunker is None:
        raise HTTPException(status_code=503, detail="Models not loaded")
    
    return {
        "status": "healthy",
        "model_loaded": embedding_model.is_loaded(),
        "chunker_ready": text_chunker.is_ready()
    }


@app.post("/chunk", response_model=ChunkingResponse)
async def chunk_text(request: ChunkingRequest):
    """Chunk text into meaningful segments"""
    global text_chunker
    
    if text_chunker is None:
        raise HTTPException(status_code=503, detail="Text chunker not initialized")
    
    try:
        chunks = await text_chunker.chunk_text(
            text=request.text,
            chunk_size=request.chunk_size,
            overlap=request.overlap,
            method=request.method
        )
        
        metadata = {
            "original_length": len(request.text),
            "num_chunks": len(chunks),
            "method": request.method,
            "chunk_size": request.chunk_size,
            "overlap": request.overlap
        }
        
        return ChunkingResponse(chunks=chunks, metadata=metadata)
    
    except Exception as e:
        logger.error(f"Error chunking text: {e}")
        raise HTTPException(status_code=500, detail=f"Error chunking text: {str(e)}")


@app.post("/embed", response_model=EmbeddingResponse)
async def embed_text(request: EmbeddingRequest):
    """Generate embeddings for text(s)"""
    global embedding_model
    
    if embedding_model is None:
        raise HTTPException(status_code=503, detail="Embedding model not initialized")
    
    try:
        # Handle both single text and list of texts
        if isinstance(request.text, str):
            texts = [request.text]
        else:
            texts = request.text
        
        embeddings = await embedding_model.embed_texts(
            texts=texts,
            normalize=request.normalize
        )
        
        metadata = {
            "num_texts": len(texts),
            "embedding_dim": len(embeddings[0]) if embeddings else 0,
            "model": settings.MODEL_NAME,
            "normalized": request.normalize
        }
        
        return EmbeddingResponse(embeddings=embeddings, metadata=metadata)
    
    except Exception as e:
        logger.error(f"Error embedding text: {e}")
        raise HTTPException(status_code=500, detail=f"Error embedding text: {str(e)}")


@app.post("/embed-chunks")
async def embed_chunks(request: ChunkingRequest):
    """Chunk text and then embed each chunk"""
    global text_chunker, embedding_model
    
    if text_chunker is None or embedding_model is None:
        raise HTTPException(status_code=503, detail="Models not initialized")
    
    try:
        # First chunk the text
        chunks = await text_chunker.chunk_text(
            text=request.text,
            chunk_size=request.chunk_size,
            overlap=request.overlap,
            method=request.method
        )
        
        # Then embed each chunk
        embeddings = await embedding_model.embed_texts(texts=chunks, normalize=True)
        
        metadata = {
            "original_length": len(request.text),
            "num_chunks": len(chunks),
            "embedding_dim": len(embeddings[0]) if embeddings else 0,
            "method": request.method,
            "chunk_size": request.chunk_size,
            "overlap": request.overlap
        }
        
        return {
            "chunks": chunks,
            "embeddings": embeddings,
            "metadata": metadata
        }
    
    except Exception as e:
        logger.error(f"Error processing text: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing text: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
