import asyncio
import logging
import re
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor

from ..config import settings

logger = logging.getLogger(__name__)

# Constants for embedding compatibility
MIN_EMBEDDING_TOKENS = 4  # Minimum tokens required for embedding
MIN_EMBEDDING_CHARS = 20  # Rough estimate: 4 tokens ≈ 20 chars
SAFE_MIN_CHUNK_SIZE = 50  # Safe minimum to ensure meaningful content


def _lazy_import_nltk():
    """Lazy import NLTK to avoid slow startup"""
    try:
        import nltk
        return nltk
    except ImportError:
        logger.warning("NLTK not available, using fallback sentence splitting")
        return None


class TextChunker:
    """Text chunking service with semantic and fixed-size chunking methods"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=2)
        self._ready = False
        
    async def initialize(self):
        """Initialize TextChunker"""
        if self._ready:
            return

        try:
            # Simple initialization without NLTK dependency
            self._ready = True
            logger.info("TextChunker initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize TextChunker: {e}")
            self._ready = True
    
    def _download_nltk_data(self):
        """Download required NLTK data"""
        try:
            nltk = _lazy_import_nltk()
            if nltk:
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
        except Exception as e:
            logger.warning(f"Could not download NLTK data: {e}")
    
    def is_ready(self) -> bool:
        """Check if chunker is ready"""
        return self._ready
    
    async def chunk_text(
        self,
        text: str,
        chunk_size: int = None,
        overlap: int = None,
        method: str = "semantic"
    ) -> List[str]:
        """
        Chunk text using specified method with embedding compatibility

        Args:
            text: Input text to chunk
            chunk_size: Maximum size of each chunk
            overlap: Overlap between chunks
            method: Chunking method ('semantic' or 'fixed')
        """
        if not text.strip():
            return []

        # Validate and set defaults
        chunk_size = chunk_size or settings.DEFAULT_CHUNK_SIZE
        overlap = overlap or settings.DEFAULT_OVERLAP

        # Ensure minimum chunk size for embedding compatibility
        min_chunk_size = max(SAFE_MIN_CHUNK_SIZE, settings.MIN_CHUNK_SIZE)
        if chunk_size < min_chunk_size:
            logger.warning(f"Chunk size {chunk_size} too small, using minimum {min_chunk_size}")
            chunk_size = min_chunk_size

        # Ensure chunker is initialized
        if not self._ready:
            await self.initialize()

        try:
            if method == "semantic":
                chunks = await self._semantic_chunk(text, chunk_size, overlap)
            elif method == "fixed":
                chunks = await self._fixed_chunk(text, chunk_size, overlap)
            else:
                raise ValueError(f"Unknown chunking method: {method}")

            # Post-process to ensure embedding compatibility
            return self._ensure_embedding_compatibility(chunks, min_chunk_size)

        except Exception as e:
            logger.error(f"Error chunking text: {e}")
            # Fallback to simple fixed chunking
            chunks = await self._simple_chunk(text, chunk_size, overlap)
            return self._ensure_embedding_compatibility(chunks, min_chunk_size)
    
    async def _semantic_chunk(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Semantic chunking based on sentences and paragraphs"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self._semantic_chunk_sync,
            text,
            chunk_size,
            overlap
        )
    
    def _semantic_chunk_sync(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Optimized semantic chunking with balanced chunk sizes"""
        # Split into paragraphs first
        paragraphs = re.split(r'\n\s*\n', text.strip())

        # First pass: collect all sentences
        all_sentences = []
        for paragraph in paragraphs:
            # Use simple regex-based sentence splitting for better performance
            sentences = re.split(r'[.!?]+', paragraph)
            sentences = [s.strip() for s in sentences if s.strip()]

            for sentence in sentences:
                sentence = sentence.strip()
                if sentence:
                    all_sentences.append(sentence)

        if not all_sentences:
            return []

        # Calculate optimal chunk parameters for balanced retrieval
        total_length = sum(len(s) for s in all_sentences)

        # Use target ratio from config for better balance
        target_chunk_size = int(chunk_size * settings.TARGET_CHUNK_RATIO)
        min_chunk_size = max(SAFE_MIN_CHUNK_SIZE, settings.MIN_CHUNK_SIZE, chunk_size // 8)

        # Estimate optimal number of chunks
        if total_length <= chunk_size:
            optimal_chunk_size = total_length
        else:
            estimated_chunks = max(1, total_length // target_chunk_size)
            optimal_chunk_size = total_length // estimated_chunks

            # Ensure within acceptable bounds
            optimal_chunk_size = max(min_chunk_size, min(optimal_chunk_size, target_chunk_size))

        chunks = []
        sentence_buffer = []

        for i, sentence in enumerate(all_sentences):
            sentence_buffer.append(sentence)
            potential_chunk = " ".join(sentence_buffer)

            # Smart decision on when to finalize chunk for optimal balance
            should_finalize = False

            # Check various conditions for finalizing
            if len(potential_chunk) >= optimal_chunk_size:
                # Reached optimal size
                should_finalize = True
            elif len(potential_chunk) >= chunk_size * 0.85:
                # Near max size, finalize to avoid overflow
                should_finalize = True
            elif i == len(all_sentences) - 1:
                # Last sentence, must finalize
                should_finalize = True
            elif len(potential_chunk) >= optimal_chunk_size * 0.8:
                # Check if next sentences would make it too long
                remaining_sentences = all_sentences[i+1:i+3]  # Look ahead 2 sentences
                if remaining_sentences:
                    next_length = sum(len(s) for s in remaining_sentences)
                    if len(potential_chunk) + next_length > chunk_size:
                        should_finalize = True

            if should_finalize and sentence_buffer:
                chunk_text = " ".join(sentence_buffer).strip()

                # Handle overlap with previous chunk
                if chunks and overlap > 0:
                    prev_chunk = chunks[-1]
                    overlap_text = self._get_overlap_text(prev_chunk, overlap)
                    if overlap_text:
                        chunk_text = overlap_text + " " + chunk_text

                chunks.append(chunk_text)
                sentence_buffer = []

        # Post-process to balance chunk sizes
        return self._balance_chunk_sizes(chunks, chunk_size, overlap)

    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """Extract overlap text from the end of previous chunk"""
        if not text or overlap_size <= 0:
            return ""

        # Try to get overlap at word boundaries
        words = text.split()
        if not words:
            return ""

        overlap_text = ""
        current_length = 0

        for word in reversed(words):
            word_length = len(word) + 1  # +1 for space
            if current_length + word_length <= overlap_size:
                overlap_text = word + " " + overlap_text if overlap_text else word
                current_length += word_length
            else:
                break

        return overlap_text.strip()

    def _balance_chunk_sizes(self, chunks: List[str], max_size: int, overlap: int) -> List[str]:
        """Balance chunk sizes to be more uniform"""
        if not chunks:
            return chunks

        balanced_chunks = []

        for chunk in chunks:
            if len(chunk) <= max_size:
                balanced_chunks.append(chunk)
            else:
                # Split oversized chunks
                sub_chunks = self._split_oversized_chunk(chunk, max_size, overlap)
                balanced_chunks.extend(sub_chunks)

        # Merge undersized chunks if possible
        return self._merge_undersized_chunks(balanced_chunks, max_size)

    def _split_oversized_chunk(self, chunk: str, max_size: int, overlap: int) -> List[str]:
        """Split an oversized chunk into smaller balanced pieces"""
        if len(chunk) <= max_size:
            return [chunk]

        # Calculate optimal number of sub-chunks
        num_chunks = (len(chunk) + max_size - 1) // max_size
        target_size = len(chunk) // num_chunks

        sentences = re.split(r'[.!?]+', chunk)
        sentences = [s.strip() for s in sentences if s.strip()]

        if not sentences:
            # Fallback to word-based splitting
            return self._split_by_words(chunk, max_size, overlap)

        sub_chunks = []
        current_chunk = ""

        for sentence in sentences:
            potential_chunk = current_chunk + " " + sentence if current_chunk else sentence

            if len(potential_chunk) <= target_size or not current_chunk:
                current_chunk = potential_chunk
            else:
                if current_chunk:
                    sub_chunks.append(current_chunk.strip())
                current_chunk = sentence

        if current_chunk.strip():
            sub_chunks.append(current_chunk.strip())

        return sub_chunks

    def _split_by_words(self, text: str, max_size: int, overlap: int) -> List[str]:
        """Split text by words when sentence splitting fails"""
        words = text.split()
        chunks = []
        current_chunk = []
        current_length = 0

        for word in words:
            word_length = len(word) + 1

            if current_length + word_length <= max_size:
                current_chunk.append(word)
                current_length += word_length
            else:
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                current_chunk = [word]
                current_length = len(word)

        if current_chunk:
            chunks.append(" ".join(current_chunk))

        return chunks

    def _merge_undersized_chunks(self, chunks: List[str], max_size: int) -> List[str]:
        """Merge chunks that are too small to create embedding-compatible sizes"""
        if not chunks:
            return chunks

        # Use embedding-safe minimum size
        min_size = max(SAFE_MIN_CHUNK_SIZE, max_size * 0.2)
        merged_chunks = []
        current_chunk = ""

        for chunk in chunks:
            chunk = chunk.strip()
            if not chunk:
                continue

            if not current_chunk:
                current_chunk = chunk
            elif len(current_chunk) + len(chunk) + 1 <= max_size:
                # Can merge with current chunk
                current_chunk = current_chunk + " " + chunk
            else:
                # Cannot merge, finalize current chunk
                if self._is_meaningful_content(current_chunk) and len(current_chunk) >= min_size:
                    merged_chunks.append(current_chunk)
                elif merged_chunks and len(merged_chunks[-1]) + len(current_chunk) + 1 <= max_size * 1.2:
                    # Merge with previous chunk if possible (allow slight overflow)
                    merged_chunks[-1] = merged_chunks[-1] + " " + current_chunk
                elif len(current_chunk) >= MIN_EMBEDDING_CHARS:
                    # Keep chunk if it has minimum chars, even if not ideal
                    merged_chunks.append(current_chunk)
                # Otherwise discard chunk that's too small

                current_chunk = chunk

        # Handle the last chunk
        if current_chunk:
            if self._is_meaningful_content(current_chunk) and len(current_chunk) >= min_size:
                merged_chunks.append(current_chunk)
            elif merged_chunks and len(merged_chunks[-1]) + len(current_chunk) + 1 <= max_size * 1.2:
                merged_chunks[-1] = merged_chunks[-1] + " " + current_chunk
            elif len(current_chunk) >= MIN_EMBEDDING_CHARS:
                merged_chunks.append(current_chunk)

        return merged_chunks

    def _ensure_embedding_compatibility(self, chunks: List[str], min_size: int) -> List[str]:
        """Ensure all chunks are compatible with embedding models"""
        if not chunks:
            return chunks

        compatible_chunks = []

        for chunk in chunks:
            chunk = chunk.strip()
            if not chunk:
                continue

            # Check if chunk meets minimum requirements
            if len(chunk) < MIN_EMBEDDING_CHARS:
                logger.debug(f"Chunk too short for embedding: {len(chunk)} chars")
                continue

            # Estimate token count (rough: 1 token ≈ 4-5 chars for Vietnamese/English)
            estimated_tokens = len(chunk) // 4
            if estimated_tokens < MIN_EMBEDDING_TOKENS:
                logger.debug(f"Chunk has too few tokens: ~{estimated_tokens} tokens")
                continue

            # Check word count (minimum 3 words for meaningful content)
            word_count = len(chunk.split())
            if word_count < 3:
                logger.debug(f"Chunk has too few words: {word_count} words")
                continue

            compatible_chunks.append(chunk)

        # If no chunks meet requirements, try to merge small chunks
        if not compatible_chunks and chunks:
            logger.warning("No chunks meet embedding requirements, attempting to merge")
            compatible_chunks = self._merge_small_chunks(chunks, min_size)

        # Final validation
        final_chunks = []
        for chunk in compatible_chunks:
            if len(chunk) >= min_size and len(chunk.split()) >= 3:
                final_chunks.append(chunk)
            else:
                logger.debug(f"Filtered out chunk: {len(chunk)} chars, {len(chunk.split())} words")

        return final_chunks

    def _merge_small_chunks(self, chunks: List[str], min_size: int) -> List[str]:
        """Merge small chunks to meet minimum size requirements"""
        if not chunks:
            return []

        merged = []
        current_chunk = ""

        for chunk in chunks:
            chunk = chunk.strip()
            if not chunk:
                continue

            if not current_chunk:
                current_chunk = chunk
            elif len(current_chunk) + len(chunk) + 1 <= min_size * 3:  # Allow up to 3x min_size
                current_chunk = current_chunk + " " + chunk
            else:
                # Finalize current chunk if it meets requirements
                if len(current_chunk) >= min_size:
                    merged.append(current_chunk)
                elif merged:  # Merge with previous chunk if possible
                    merged[-1] = merged[-1] + " " + current_chunk
                else:  # First chunk, keep even if small
                    merged.append(current_chunk)

                current_chunk = chunk

        # Handle the last chunk
        if current_chunk:
            if len(current_chunk) >= min_size:
                merged.append(current_chunk)
            elif merged:
                merged[-1] = merged[-1] + " " + current_chunk
            else:
                merged.append(current_chunk)

        return merged

    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count for text"""
        # Rough estimation: 1 token ≈ 4-5 characters for mixed Vietnamese/English
        # This is conservative to ensure we don't underestimate
        return max(1, len(text) // 4)

    def _is_meaningful_content(self, text: str) -> bool:
        """Check if text contains meaningful content for embedding"""
        if not text or len(text.strip()) < MIN_EMBEDDING_CHARS:
            return False

        # Check for minimum word count
        words = text.split()
        if len(words) < 3:
            return False

        # Check for non-whitespace content
        non_space_chars = len([c for c in text if not c.isspace()])
        if non_space_chars < MIN_EMBEDDING_CHARS:
            return False

        # Check for meaningful characters (not just punctuation)
        meaningful_chars = len([c for c in text if c.isalnum()])
        if meaningful_chars < MIN_EMBEDDING_CHARS // 2:
            return False

        return True
    
    def _split_long_sentence(self, sentence: str, chunk_size: int, overlap: int) -> List[str]:
        """Split a long sentence into smaller chunks"""
        words = sentence.split()
        chunks = []
        current_chunk = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1  # +1 for space
            
            if current_length + word_length <= chunk_size:
                current_chunk.append(word)
                current_length += word_length
            else:
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                    
                    # Handle overlap
                    if overlap > 0:
                        overlap_words = []
                        overlap_length = 0
                        for w in reversed(current_chunk):
                            if overlap_length + len(w) + 1 <= overlap:
                                overlap_words.insert(0, w)
                                overlap_length += len(w) + 1
                            else:
                                break
                        current_chunk = overlap_words + [word]
                        current_length = sum(len(w) + 1 for w in current_chunk)
                    else:
                        current_chunk = [word]
                        current_length = len(word)
                else:
                    # Single word is too long, just add it
                    chunks.append(word)
                    current_chunk = []
                    current_length = 0
        
        if current_chunk:
            chunks.append(" ".join(current_chunk))
        
        return chunks
    
    async def _fixed_chunk(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Fixed-size chunking"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self._fixed_chunk_sync,
            text,
            chunk_size,
            overlap
        )
    
    def _fixed_chunk_sync(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Synchronous fixed-size chunking"""
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            
            # Try to break at word boundary
            if end < len(text) and not text[end].isspace():
                last_space = chunk.rfind(' ')
                if last_space > chunk_size * 0.8:  # Only break if we don't lose too much
                    chunk = chunk[:last_space]
                    end = start + last_space
            
            chunks.append(chunk.strip())
            start = max(start + chunk_size - overlap, end)
            
            if start >= len(text):
                break
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    async def _simple_chunk(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Simple fallback chunking"""
        chunks = []
        words = text.split()
        current_chunk = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1
            
            if current_length + word_length <= chunk_size:
                current_chunk.append(word)
                current_length += word_length
            else:
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                current_chunk = [word]
                current_length = len(word)
        
        if current_chunk:
            chunks.append(" ".join(current_chunk))
        
        return chunks
