#!/usr/bin/env python3
"""
Windows-specific installation script for BGE-M3 Embedding Server
Handles compatibility issues with Python 3.13 and Windows
"""

import subprocess
import sys
import platform
import os

def run_command(command, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    if description:
        print(f"📦 {description}")
    print(f"Running: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def check_python_version():
    """Check Python version compatibility"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3:
        print("❌ Python 3 is required")
        return False
    
    if version.minor >= 13:
        print("⚠️  Python 3.13+ detected. Some packages may need special handling.")
    
    return True

def install_torch_first():
    """Install PyTorch first with CPU support"""
    print("\n🔥 Installing PyTorch (CPU version)...")
    
    # For Python 3.13, we need to use a compatible torch version
    if sys.version_info.minor >= 13:
        torch_command = "pip install torch==2.1.2+cpu torchvision==0.16.2+cpu torchaudio==2.1.2+cpu --index-url https://download.pytorch.org/whl/cpu"
    else:
        torch_command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    
    return run_command(torch_command, "Installing PyTorch")

def install_core_packages():
    """Install core packages that usually work"""
    core_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "pydantic>=2.0.0",
        "python-multipart==0.0.6",
        "aiofiles==23.2.1",
        "python-dotenv==1.0.0",
        "loguru==0.7.2",
        "numpy>=1.21.0,<2.0.0",
        "requests>=2.28.0"
    ]
    
    print("\n📦 Installing core packages...")
    for package in core_packages:
        success = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️  Failed to install {package}, continuing...")

def install_ml_packages():
    """Install ML packages with special handling"""
    print("\n🤖 Installing ML packages...")
    
    # Install transformers first
    if not run_command("pip install transformers>=4.35.0", "Installing transformers"):
        print("⚠️  Failed to install transformers")
        return False
    
    # Install tokenizers
    if not run_command("pip install tokenizers>=0.15.0", "Installing tokenizers"):
        print("⚠️  Failed to install tokenizers")
    
    # Install huggingface-hub
    if not run_command("pip install huggingface-hub>=0.19.0", "Installing huggingface-hub"):
        print("⚠️  Failed to install huggingface-hub")
    
    # Try to install sentencepiece with fallback
    print("\n🔤 Installing sentencepiece...")
    sentencepiece_commands = [
        "pip install sentencepiece>=0.1.99",
        "pip install sentencepiece --no-build-isolation",
        "pip install sentencepiece --only-binary=all",
    ]
    
    sentencepiece_installed = False
    for cmd in sentencepiece_commands:
        if run_command(cmd, f"Trying: {cmd}"):
            sentencepiece_installed = True
            break
    
    if not sentencepiece_installed:
        print("❌ Could not install sentencepiece. Trying alternative approach...")
        # Try installing pre-compiled wheel
        run_command("pip install https://github.com/google/sentencepiece/releases/download/v0.1.99/sentencepiece-0.1.99-cp311-cp311-win_amd64.whl", "Installing pre-compiled sentencepiece")
    
    # Finally, try sentence-transformers
    print("\n🔄 Installing sentence-transformers...")
    st_commands = [
        "pip install sentence-transformers==2.7.0",
        "pip install sentence-transformers>=2.2.0",
        "pip install sentence-transformers --no-deps",
    ]
    
    for cmd in st_commands:
        if run_command(cmd, f"Trying: {cmd}"):
            break
    
    return True

def install_optional_packages():
    """Install optional packages"""
    optional_packages = [
        "nltk>=3.8.1",
        "regex>=2023.10.3"
    ]
    
    print("\n📚 Installing optional packages...")
    for package in optional_packages:
        run_command(f"pip install {package}", f"Installing {package}")

def verify_installation():
    """Verify that key packages are installed"""
    print("\n🔍 Verifying installation...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "torch",
        "transformers",
        "sentence_transformers",
        "numpy",
        "pydantic"
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  Failed to import: {', '.join(failed_packages)}")
        print("You may need to install these manually or use Docker instead.")
        return False
    else:
        print("\n🎉 All required packages are installed!")
        return True

def main():
    """Main installation process"""
    print("🚀 BGE-M3 Embedding Server - Windows Installation")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if we're on Windows
    if platform.system() != "Windows":
        print("⚠️  This script is designed for Windows. Use regular pip install on other systems.")
    
    # Upgrade pip first
    run_command("python -m pip install --upgrade pip", "Upgrading pip")
    
    # Install packages in order
    steps = [
        ("Installing PyTorch", install_torch_first),
        ("Installing core packages", install_core_packages),
        ("Installing ML packages", install_ml_packages),
        ("Installing optional packages", install_optional_packages),
        ("Verifying installation", verify_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'🔄' * 3} {step_name} {'🔄' * 3}")
        try:
            success = step_func()
            if step_name == "Verifying installation" and not success:
                print("\n⚠️  Some packages failed to install. Consider using Docker instead.")
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 Installation completed!")
    print("\nNext steps:")
    print("1. Run: python -c \"import sentence_transformers; print('✅ Ready!')\"")
    print("2. Start the server: uvicorn app.main:app --reload")
    print("3. Or use Docker: docker-compose up --build")

if __name__ == "__main__":
    main()
