# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
docs/
*.md

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Models (will be downloaded at runtime)
models/
*.bin
*.safetensors

# Logs
logs/
*.log

# Environment files
.env
.env.local
.env.*.local

# Testing
tests/
test_*
*_test.py
