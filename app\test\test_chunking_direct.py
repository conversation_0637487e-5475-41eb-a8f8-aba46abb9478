#!/usr/bin/env python3
"""
Direct test of chunking functionality without server
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.text_chunker import TextChunker
from app.config import settings

async def test_chunking_direct():
    """Test chunking directly without server"""
    
    print("🔍 Testing Optimized Chunking (Direct)")
    print("=" * 60)
    
    # Initialize chunker
    chunker = TextChunker()
    await chunker.initialize()
    
    # Test text
    test_text = """
    Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
    có thể thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> bao gồ<PERSON> họ<PERSON> tậ<PERSON>, 
    <PERSON><PERSON>, g<PERSON><PERSON><PERSON> quyết vấn đề, nhận thức và hiểu ngôn ngữ tự nhiên.
    
    Machine learning là một nhánh con của AI tập trung vào việc phát triển các thuật toán và mô hình 
    thống kê cho phép máy tính học và cải thiện hiệu suất từ dữ liệu mà không cần được lập trình 
    một cách rõ ràng cho từng nhiệm vụ cụ thể.
    
    Deep learning là một phương pháp machine learning sử dụng mạng neural nhân tạo với nhiều lớp 
    để mô hình hóa và hiểu dữ liệu phức tạp. Nó đã đạt được những thành công đáng kể trong nhiều 
    lĩnh vực như xử lý hình ảnh, nhận dạng giọng nói và xử lý ngôn ngữ tự nhiên.
    
    Natural Language Processing (NLP) là một lĩnh vực con của trí tuệ nhân tạo tập trung vào 
    việc cho phép máy tính hiểu, diễn giải và tạo ra ngôn ngữ tự nhiên của con người. 
    NLP kết hợp khoa học máy tính, trí tuệ nhân tạo và ngôn ngữ học để xử lý và phân tích 
    lượng lớn dữ liệu ngôn ngữ tự nhiên.
    """
    
    # Test different configurations
    test_configs = [
        {"chunk_size": 200, "overlap": 30, "method": "semantic"},
        {"chunk_size": 300, "overlap": 50, "method": "semantic"},
        {"chunk_size": 384, "overlap": 64, "method": "semantic"},  # Default optimized
        {"chunk_size": 500, "overlap": 80, "method": "semantic"},
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n📊 Test {i}: chunk_size={config['chunk_size']}, overlap={config['overlap']}")
        print("-" * 50)
        
        try:
            chunks = await chunker.chunk_text(
                text=test_text,
                chunk_size=config['chunk_size'],
                overlap=config['overlap'],
                method=config['method']
            )
            
            print(f"✅ Status: Success")
            print(f"📈 Total chunks: {len(chunks)}")
            print(f"📏 Original length: {len(test_text)} chars")
            
            # Analyze chunk balance
            chunk_lengths = [len(chunk) for chunk in chunks]
            if chunk_lengths:
                avg_length = sum(chunk_lengths) / len(chunk_lengths)
                min_length = min(chunk_lengths)
                max_length = max(chunk_lengths)
                
                print(f"📊 Chunk lengths - Avg: {avg_length:.1f}, Min: {min_length}, Max: {max_length}")
                print(f"⚖️  Balance ratio: {min_length/max_length:.2f} (closer to 1.0 = better balance)")
                
                # Show chunks with lengths
                print(f"\n📝 Chunks:")
                for j, chunk in enumerate(chunks):
                    print(f"  Chunk {j+1} ({len(chunk)} chars): {chunk.strip()}")
                    print()
            else:
                print("❌ No chunks generated")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            import traceback
            traceback.print_exc()
    
    # Test edge cases
    print(f"\n\n🔍 Testing Edge Cases")
    print("=" * 60)
    
    edge_cases = [
        {
            "name": "Very short text",
            "text": "Short text.",
            "chunk_size": 100
        },
        {
            "name": "Single long sentence",
            "text": "Đây là một câu rất dài với nhiều thông tin chi tiết về trí tuệ nhân tạo, machine learning, deep learning và các ứng dụng của chúng trong thực tế như xử lý ngôn ngữ tự nhiên, computer vision, và robotics mà không có dấu chấm câu nào cả",
            "chunk_size": 150
        },
        {
            "name": "Multiple short sentences",
            "text": "Câu một. Câu hai. Câu ba. Câu bốn. Câu năm. Câu sáu. Câu bảy. Câu tám.",
            "chunk_size": 50
        },
        {
            "name": "Extremely short chunks (problematic for embedding)",
            "text": "A. B. C. D. E. F.",
            "chunk_size": 20
        },
        {
            "name": "Mixed short and long content",
            "text": "AI. Machine learning là một lĩnh vực quan trọng. NLP. Deep learning sử dụng neural networks để xử lý dữ liệu phức tạp và tạo ra các mô hình có khả năng học hỏi từ dữ liệu.",
            "chunk_size": 80
        }
    ]
    
    for case in edge_cases:
        print(f"\n📝 Testing: {case['name']}")
        print(f"Text: {case['text']}")
        print("-" * 30)
        
        try:
            chunks = await chunker.chunk_text(
                text=case['text'],
                chunk_size=case.get('chunk_size', 384),
                overlap=20,
                method="semantic"
            )
            
            chunk_lengths = [len(chunk) for chunk in chunks]
            print(f"Chunks: {len(chunks)}, Lengths: {chunk_lengths}")
            
            for j, chunk in enumerate(chunks):
                print(f"  {j+1}. ({len(chunk)}): {chunk}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_chunking_direct())
