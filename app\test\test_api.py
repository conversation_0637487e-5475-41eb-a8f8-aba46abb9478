#!/usr/bin/env python3
"""
Simple test script for the BGE-M3 Embedding Server API
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_chunking():
    """Test text chunking endpoint"""
    print("\nTesting text chunking...")
    
    sample_text = """
    Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
    có thể thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. <PERSON><PERSON><PERSON><PERSON> nà<PERSON> bao gồm họ<PERSON> tậ<PERSON>, 
    l<PERSON> luậ<PERSON>, g<PERSON><PERSON><PERSON> quyết vấn đề, nhận thức và hiểu ngôn ngữ tự nhiên.
    
    Machine learning là một nhánh con của AI tập trung vào việc phát triển các thuật toán và mô hình 
    thống kê cho phép máy tính học và cải thiện hiệu suất từ dữ liệu mà không cần được lập trình 
    một cách rõ ràng cho từng nhiệm vụ cụ thể.
    
    Deep learning là một phương pháp machine learning sử dụng mạng neural nhân tạo với nhiều lớp 
    để mô hình hóa và hiểu dữ liệu phức tạp. Nó đã đạt được những thành công đáng kể trong nhiều 
    lĩnh vực như xử lý hình ảnh, nhận dạng giọng nói và xử lý ngôn ngữ tự nhiên.
    """
    
    payload = {
        "text": sample_text,
        "chunk_size": 200,
        "overlap": 30,
        "method": "semantic"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/chunk", json=payload)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Number of chunks: {result['metadata']['num_chunks']}")
            for i, chunk in enumerate(result['chunks']):
                print(f"Chunk {i+1}: {chunk}...")
        else:
            print(f"Error: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_embedding():
    """Test text embedding endpoint"""
    print("\nTesting text embedding...")
    
    payload = {
        "text": "Đây là một câu tiếng Việt để test embedding.",
        "normalize": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/embed", json=payload)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Embedding dimension: {result['metadata']['embedding_dim']}")
            print(f"First 5 values: {result['embeddings'][0][:5]}")
        else:
            print(f"Error: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_batch_embedding():
    """Test batch embedding"""
    print("\nTesting batch embedding...")
    
    payload = {
        "text": [
            "Câu thứ nhất để test.",
            "Câu thứ hai để test.",
            "Câu thứ ba để test."
        ],
        "normalize": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/embed", json=payload)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Number of embeddings: {result['metadata']['num_texts']}")
            print(f"Embedding dimension: {result['metadata']['embedding_dim']}")
        else:
            print(f"Error: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_embed_chunks():
    """Test chunk and embed endpoint"""
    print("\nTesting chunk and embed...")
    
    sample_text = """
    Natural Language Processing (NLP) là một lĩnh vực con của trí tuệ nhân tạo tập trung vào 
    việc cho phép máy tính hiểu, diễn giải và tạo ra ngôn ngữ tự nhiên của con người. 
    NLP kết hợp khoa học máy tính, trí tuệ nhân tạo và ngôn ngữ học để xử lý và phân tích 
    lượng lớn dữ liệu ngôn ngữ tự nhiên.
    """
    
    payload = {
        "text": sample_text,
        "chunk_size": 150,
        "overlap": 20,
        "method": "semantic"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/embed-chunks", json=payload)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Number of chunks: {result['metadata']['num_chunks']}")
            print(f"Embedding dimension: {result['metadata']['embedding_dim']}")
        else:
            print(f"Error: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Run all tests"""
    print("Starting API tests...")
    print("=" * 50)
    
    # Wait for server to be ready
    print("Waiting for server to be ready...")
    for i in range(30):  # Wait up to 30 seconds
        try:
            response = requests.get(f"{BASE_URL}/")
            if response.status_code == 200:
                print("Server is ready!")
                break
        except:
            pass
        time.sleep(1)
        print(f"Waiting... ({i+1}/30)")
    else:
        print("Server not ready after 30 seconds")
        return
    
    # Run tests
    tests = [
        ("Health Check", test_health),
        ("Text Chunking", test_chunking),
        ("Text Embedding", test_embedding),
        ("Batch Embedding", test_batch_embedding),
        ("Chunk and Embed", test_embed_chunks)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\nTotal: {passed}/{total} tests passed")

if __name__ == "__main__":
    main()
