# BGE-M3 Embedding Server

Một FastAPI server để tạo embeddings và chia nhỏ text sử dụng model BGE-M3.

## Tính năng

- **Text Embedding**: Tạo embeddings cho text sử dụng model BGE-M3
- **Text Chunking**: Chia text thành các đoạn có nghĩa
- **Batch Processing**: Hỗ trợ xử lý nhiều text cùng lúc
- **Docker Support**: <PERSON><PERSON> dàng deploy với Docker
- **Health Check**: Endpoint kiểm tra sức khỏe của service
- **Auto Documentation**: Swagger UI tự động

## Cài đặt

### Sử dụng Docker (Khuyến nghị)

1. Clone repository:
```bash
git clone <repository-url>
cd embedding-server
```

2. Build và chạy với Docker Compose:
```bash
docker-compose up --build
```

3. Server sẽ chạy tại: http://localhost:8000

### Cài đặt thủ công

#### Windows (Python 3.13+)
Nếu bạn gặp lỗi với `sentencepiece` trên Windows:

1. Sử dụng script cài đặt tự động:
```bash
python install_windows.py
```

2. Hoặc cài đặt thủ công:
```bash
pip install -r requirements-windows.txt
```

#### Linux/macOS
```bash
pip install -r requirements.txt
```

#### Chạy server
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## API Endpoints

### 1. Health Check
```
GET /health
```

### 2. Text Chunking
```
POST /chunk
```

**Request Body:**
```json
{
  "text": "Văn bản cần chia nhỏ...",
  "chunk_size": 512,
  "overlap": 50,
  "method": "semantic"
}
```

**Response:**
```json
{
  "chunks": ["Đoạn 1...", "Đoạn 2..."],
  "metadata": {
    "original_length": 1000,
    "num_chunks": 2,
    "method": "semantic",
    "chunk_size": 512,
    "overlap": 50
  }
}
```

### 3. Text Embedding
```
POST /embed
```

**Request Body:**
```json
{
  "text": "Text cần tạo embedding",
  "normalize": true
}
```

Hoặc với nhiều text:
```json
{
  "text": ["Text 1", "Text 2", "Text 3"],
  "normalize": true
}
```

**Response:**
```json
{
  "embeddings": [[0.1, 0.2, ...], [0.3, 0.4, ...]],
  "metadata": {
    "num_texts": 2,
    "embedding_dim": 1024,
    "model": "BAAI/bge-m3",
    "normalized": true
  }
}
```

### 4. Chunk và Embed
```
POST /embed-chunks
```

Chia text thành chunks và tạo embedding cho từng chunk.

## Cấu hình

Tạo file `.env` để cấu hình:

```env
MODEL_NAME=BAAI/bge-m3
DEVICE=auto
LOG_LEVEL=INFO
MAX_TEXT_LENGTH=8192
DEFAULT_CHUNK_SIZE=512
DEFAULT_OVERLAP=50
BATCH_SIZE=32
MAX_CONCURRENT_REQUESTS=10
```

## Chunking Methods

### Semantic Chunking
- Chia text dựa trên cấu trúc ngữ nghĩa
- Ưu tiên giữ nguyên câu và đoạn văn
- Phù hợp cho text có cấu trúc rõ ràng

### Fixed Chunking
- Chia text theo kích thước cố định
- Cố gắng chia tại ranh giới từ
- Phù hợp cho text không có cấu trúc rõ ràng

## Performance

- **GPU Support**: Tự động sử dụng GPU nếu có
- **Batch Processing**: Xử lý nhiều text cùng lúc
- **Async Processing**: Non-blocking operations
- **Memory Optimization**: Efficient model loading

## Docker Build

```bash
# Build image
docker build -t bge-m3-embedding-server .

# Run container
docker run -p 8000:8000 bge-m3-embedding-server
```

## API Documentation

Khi server đang chạy, truy cập:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Monitoring

- Health check endpoint: `/health`
- Logs được lưu trong thư mục `logs/`
- Metrics có thể được monitor qua FastAPI

## Troubleshooting

### Windows Installation Issues

#### SentencePiece Build Error
Nếu gặp lỗi `FileNotFoundError` khi cài `sentencepiece`:

1. **Sử dụng script tự động**:
```bash
python install_windows.py
```

2. **Cài đặt Visual Studio Build Tools**:
   - Download từ: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Chọn "C++ build tools" workload

3. **Sử dụng pre-compiled wheel**:
```bash
pip install --only-binary=all sentencepiece
```

4. **Alternative: Sử dụng conda**:
```bash
conda install -c conda-forge sentencepiece
```

#### Python 3.13 Compatibility
- Một số packages chưa hỗ trợ đầy đủ Python 3.13
- Khuyến nghị sử dụng Python 3.11 hoặc 3.12
- Hoặc sử dụng Docker để tránh vấn đề compatibility

### Model Loading Issues
- Đảm bảo có đủ RAM (ít nhất 4GB)
- Kiểm tra kết nối internet để download model
- Xem logs để debug chi tiết

### Performance Issues
- Giảm `BATCH_SIZE` nếu hết memory
- Sử dụng GPU để tăng tốc
- Giảm `MAX_CONCURRENT_REQUESTS` nếu server quá tải

## License

MIT License
