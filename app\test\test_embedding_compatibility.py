#!/usr/bin/env python3
"""
Test embedding compatibility of chunks
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.text_chunker import TextChunker

async def test_embedding_compatibility():
    """Test that all chunks are compatible with embedding models"""
    
    print("🔍 Testing Embedding Compatibility")
    print("=" * 60)
    
    # Initialize chunker
    chunker = TextChunker()
    await chunker.initialize()
    
    # Test cases that previously caused issues
    problematic_texts = [
        "A.",
        "B. C.",
        "AI. ML.",
        "Short.",
        "Very short text here.",
        "Một. Hai. Ba.",
        "AI là gì? ML là gì? DL là gì?",
        "Trí tuệ nhân tạo. Machine learning. Deep learning.",
        "",
        "   ",
        "123",
        "!@#$%",
    ]
    
    print("📊 Testing Problematic Texts:")
    print("-" * 40)
    
    all_passed = True
    
    for i, text in enumerate(problematic_texts, 1):
        print(f"\nTest {i}: '{text}' ({len(text)} chars)")
        
        try:
            chunks = await chunker.chunk_text(
                text=text,
                chunk_size=384,
                overlap=64,
                method="semantic"
            )
            
            if not chunks:
                print(f"  ✅ No chunks generated (text too short) - SAFE")
                continue
            
            # Validate each chunk
            chunk_valid = True
            for j, chunk in enumerate(chunks):
                chunk_len = len(chunk)
                word_count = len(chunk.split())
                estimated_tokens = chunk_len // 4
                
                print(f"  Chunk {j+1}: {chunk_len} chars, {word_count} words, ~{estimated_tokens} tokens")
                print(f"    Content: '{chunk[:50]}{'...' if len(chunk) > 50 else ''}'")
                
                # Check embedding requirements
                if chunk_len < 20:
                    print(f"    ❌ Too short: {chunk_len} < 20 chars")
                    chunk_valid = False
                    all_passed = False
                
                if word_count < 3:
                    print(f"    ❌ Too few words: {word_count} < 3")
                    chunk_valid = False
                    all_passed = False
                
                if estimated_tokens < 4:
                    print(f"    ❌ Too few tokens: ~{estimated_tokens} < 4")
                    chunk_valid = False
                    all_passed = False
                
                if chunk_valid:
                    print(f"    ✅ SAFE for embedding")
            
        except Exception as e:
            print(f"  ❌ Exception: {e}")
            all_passed = False
    
    # Test with normal content
    print(f"\n\n📊 Testing Normal Content:")
    print("-" * 40)
    
    normal_texts = [
        "Trí tuệ nhân tạo là một lĩnh vực quan trọng.",
        "Machine learning helps computers learn from data automatically.",
        "Deep learning sử dụng neural networks để xử lý dữ liệu phức tạp.",
        """
        Natural Language Processing (NLP) là một lĩnh vực con của trí tuệ nhân tạo 
        tập trung vào việc cho phép máy tính hiểu và xử lý ngôn ngữ tự nhiên.
        """
    ]
    
    for i, text in enumerate(normal_texts, 1):
        print(f"\nNormal Test {i}: ({len(text)} chars)")
        
        try:
            chunks = await chunker.chunk_text(
                text=text,
                chunk_size=200,
                overlap=30,
                method="semantic"
            )
            
            print(f"  Generated {len(chunks)} chunks:")
            
            for j, chunk in enumerate(chunks):
                chunk_len = len(chunk)
                word_count = len(chunk.split())
                estimated_tokens = chunk_len // 4
                
                print(f"    Chunk {j+1}: {chunk_len} chars, {word_count} words, ~{estimated_tokens} tokens")
                
                # All should be safe
                if chunk_len >= 20 and word_count >= 3 and estimated_tokens >= 4:
                    print(f"      ✅ SAFE for embedding")
                else:
                    print(f"      ❌ NOT SAFE for embedding")
                    all_passed = False
                    
        except Exception as e:
            print(f"  ❌ Exception: {e}")
            all_passed = False
    
    # Summary
    print(f"\n\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED - No Qdrant errors expected!")
        print("✅ All chunks are compatible with embedding models")
        print("✅ No chunks will cause 'OutputTooSmall' error")
    else:
        print("❌ SOME TESTS FAILED - Potential Qdrant errors!")
        print("⚠️  Some chunks may cause embedding issues")
    
    print(f"\n📋 Embedding Requirements Summary:")
    print(f"  • Minimum chars: 20 (to ensure >= 4 tokens)")
    print(f"  • Minimum words: 3 (for meaningful content)")
    print(f"  • Minimum tokens: 4 (Qdrant requirement)")
    print(f"  • Safe minimum: 50 chars (with buffer)")

if __name__ == "__main__":
    asyncio.run(test_embedding_compatibility())
